/*! tailwindcss v4.1.8 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-orange-500: oklch(70.5% 0.213 47.604);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-teal-500: oklch(70.4% 0.14 182.503);
    --color-cyan-500: oklch(71.5% 0.143 215.221);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-purple-500: oklch(62.7% 0.265 303.9);
    --color-pink-500: oklch(65.6% 0.241 354.308);
    --color-rose-500: oklch(64.5% 0.246 16.439);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -0.025em;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --blur-sm: 8px;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-primary: var(--color-primary);
    --color-primary-foreground: var(--color-primary-foreground);
    --color-secondary: var(--color-secondary);
    --color-secondary-foreground: var(--color-secondary-foreground);
    --color-accent: var(--color-accent);
    --color-accent-foreground: var(--color-accent-foreground);
    --color-background: var(--color-background);
    --color-foreground: var(--color-foreground);
    --color-muted: var(--color-muted);
    --color-muted-foreground: var(--color-muted-foreground);
    --color-border: var(--color-border);
    --color-card: var(--color-card);
    --color-card-foreground: var(--color-card-foreground);
    --color-popover: var(--color-popover);
    --color-popover-foreground: var(--color-popover-foreground);
    --color-note: var(--color-note);
    --color-tip: var(--color-tip);
    --color-important: var(--color-important);
    --color-warning: var(--color-warning);
    --color-caution: var(--color-caution);
    --reading-progress-bg: var(--color-border);
    @supports (color: color-mix(in lab, red, red)) {
      --reading-progress-bg: color-mix(
    in srgb,
    var(--color-border) 30%,
    transparent
  );
    }
    --reading-progress-shadow: var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      --reading-progress-shadow: color-mix(
    in srgb,
    var(--color-primary) 30%,
    transparent
  );
    }
    --reading-progress-height: 3px;
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-auto {
    pointer-events: auto;
  }
  .pointer-events-none {
    pointer-events: none;
  }
  .collapse {
    visibility: collapse;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-4 {
    inset: calc(var(--spacing) * 4);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .top-8 {
    top: calc(var(--spacing) * 8);
  }
  .top-12 {
    top: calc(var(--spacing) * 12);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .right-8 {
    right: calc(var(--spacing) * 8);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }
  .bottom-8 {
    bottom: calc(var(--spacing) * 8);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-2 {
    left: calc(var(--spacing) * 2);
  }
  .left-4 {
    left: calc(var(--spacing) * 4);
  }
  .left-8 {
    left: calc(var(--spacing) * 8);
  }
  .z-10 {
    z-index: 10;
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .z-\[60\] {
    z-index: 60;
  }
  .z-\[9999\] {
    z-index: 9999;
  }
  .m-0 {
    margin: calc(var(--spacing) * 0);
  }
  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }
  .mx-8 {
    margin-inline: calc(var(--spacing) * 8);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .my-8 {
    margin-block: calc(var(--spacing) * 8);
  }
  .prose {
    color: var(--tw-prose-body);
    max-width: 65ch;
    :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-lead);
      font-size: 1.25em;
      line-height: 1.6;
      margin-top: 1.2em;
      margin-bottom: 1.2em;
    }
    :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-links);
      text-decoration: underline;
      font-weight: 500;
    }
    :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-bold);
      font-weight: 600;
    }
    :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: decimal;
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-inline-start: 1.625em;
    }
    :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-alpha;
    }
    :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-alpha;
    }
    :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-alpha;
    }
    :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-alpha;
    }
    :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-roman;
    }
    :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-roman;
    }
    :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-roman;
    }
    :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-roman;
    }
    :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: decimal;
    }
    :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: disc;
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-inline-start: 1.625em;
    }
    :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
      font-weight: 400;
      color: var(--tw-prose-counters);
    }
    :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
      color: var(--tw-prose-bullets);
    }
    :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      margin-top: 1.25em;
    }
    :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-color: var(--tw-prose-hr);
      border-top-width: 1;
      margin-top: 3em;
      margin-bottom: 3em;
    }
    :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 500;
      font-style: italic;
      color: var(--tw-prose-quotes);
      border-inline-start-width: 0.25rem;
      border-inline-start-color: var(--tw-prose-quote-borders);
      quotes: "\201C""\201D""\2018""\2019";
      margin-top: 1.6em;
      margin-bottom: 1.6em;
      padding-inline-start: 1em;
    }
    :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: open-quote;
    }
    :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: close-quote;
    }
    :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 800;
      font-size: 2.25em;
      margin-top: 0;
      margin-bottom: 0.8888889em;
      line-height: 1.1111111;
    }
    :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 900;
      color: inherit;
    }
    :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 700;
      font-size: 1.5em;
      margin-top: 2em;
      margin-bottom: 1em;
      line-height: 1.3333333;
    }
    :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 800;
      color: inherit;
    }
    :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      font-size: 1.25em;
      margin-top: 1.6em;
      margin-bottom: 0.6em;
      line-height: 1.6;
    }
    :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 700;
      color: inherit;
    }
    :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      margin-top: 1.5em;
      margin-bottom: 0.5em;
      line-height: 1.5;
    }
    :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 700;
      color: inherit;
    }
    :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      display: block;
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 500;
      font-family: inherit;
      color: var(--tw-prose-kbd);
      box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
      font-size: 0.875em;
      border-radius: 0.3125rem;
      padding-top: 0.1875em;
      padding-inline-end: 0.375em;
      padding-bottom: 0.1875em;
      padding-inline-start: 0.375em;
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-code);
      font-weight: 600;
      font-size: 0.875em;
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: "`";
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: "`";
    }
    :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
      font-size: 0.875em;
    }
    :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
      font-size: 0.9em;
    }
    :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-pre-code);
      background-color: var(--tw-prose-pre-bg);
      overflow-x: auto;
      font-weight: 400;
      font-size: 0.875em;
      line-height: 1.7142857;
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
      border-radius: 0.375rem;
      padding-top: 0.8571429em;
      padding-inline-end: 1.1428571em;
      padding-bottom: 0.8571429em;
      padding-inline-start: 1.1428571em;
    }
    :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      background-color: transparent;
      border-width: 0;
      border-radius: 0;
      padding: 0;
      font-weight: inherit;
      color: inherit;
      font-size: inherit;
      font-family: inherit;
      line-height: inherit;
    }
    :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: none;
    }
    :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: none;
    }
    :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      width: 100%;
      table-layout: auto;
      margin-top: 2em;
      margin-bottom: 2em;
      font-size: 0.875em;
      line-height: 1.7142857;
    }
    :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 1px;
      border-bottom-color: var(--tw-prose-th-borders);
    }
    :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      vertical-align: bottom;
      padding-inline-end: 0.5714286em;
      padding-bottom: 0.5714286em;
      padding-inline-start: 0.5714286em;
    }
    :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 1px;
      border-bottom-color: var(--tw-prose-td-borders);
    }
    :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 0;
    }
    :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      vertical-align: baseline;
    }
    :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-top-width: 1px;
      border-top-color: var(--tw-prose-th-borders);
    }
    :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      vertical-align: top;
    }
    :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      text-align: start;
    }
    :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-captions);
      font-size: 0.875em;
      line-height: 1.4285714;
      margin-top: 0.8571429em;
    }
    --tw-prose-body: oklch(37.3% 0.034 259.733);
    --tw-prose-headings: oklch(21% 0.034 264.665);
    --tw-prose-lead: oklch(44.6% 0.03 256.802);
    --tw-prose-links: oklch(21% 0.034 264.665);
    --tw-prose-bold: oklch(21% 0.034 264.665);
    --tw-prose-counters: oklch(55.1% 0.027 264.364);
    --tw-prose-bullets: oklch(87.2% 0.01 258.338);
    --tw-prose-hr: oklch(92.8% 0.006 264.531);
    --tw-prose-quotes: oklch(21% 0.034 264.665);
    --tw-prose-quote-borders: oklch(92.8% 0.006 264.531);
    --tw-prose-captions: oklch(55.1% 0.027 264.364);
    --tw-prose-kbd: oklch(21% 0.034 264.665);
    --tw-prose-kbd-shadows: NaN NaN NaN;
    --tw-prose-code: oklch(21% 0.034 264.665);
    --tw-prose-pre-code: oklch(92.8% 0.006 264.531);
    --tw-prose-pre-bg: oklch(27.8% 0.033 256.848);
    --tw-prose-th-borders: oklch(87.2% 0.01 258.338);
    --tw-prose-td-borders: oklch(92.8% 0.006 264.531);
    --tw-prose-invert-body: oklch(87.2% 0.01 258.338);
    --tw-prose-invert-headings: #fff;
    --tw-prose-invert-lead: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-links: #fff;
    --tw-prose-invert-bold: #fff;
    --tw-prose-invert-counters: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-bullets: oklch(44.6% 0.03 256.802);
    --tw-prose-invert-hr: oklch(37.3% 0.034 259.733);
    --tw-prose-invert-quotes: oklch(96.7% 0.003 264.542);
    --tw-prose-invert-quote-borders: oklch(37.3% 0.034 259.733);
    --tw-prose-invert-captions: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-kbd: #fff;
    --tw-prose-invert-kbd-shadows: 255 255 255;
    --tw-prose-invert-code: #fff;
    --tw-prose-invert-pre-code: oklch(87.2% 0.01 258.338);
    --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
    --tw-prose-invert-th-borders: oklch(44.6% 0.03 256.802);
    --tw-prose-invert-td-borders: oklch(37.3% 0.034 259.733);
    font-size: 1rem;
    line-height: 1.75;
    :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }
    :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.375em;
    }
    :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.375em;
    }
    :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
    }
    :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.25em;
    }
    :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
    }
    :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.25em;
    }
    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5em;
      padding-inline-start: 1.625em;
    }
    :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 0;
    }
    :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-top: 0.5714286em;
      padding-inline-end: 0.5714286em;
      padding-bottom: 0.5714286em;
      padding-inline-start: 0.5714286em;
    }
    :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 0;
    }
    :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 0;
    }
  }
  .prose-sm {
    font-size: 0.875rem;
    line-height: 1.7142857;
    :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
      margin-bottom: 1.1428571em;
    }
    :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 1.2857143em;
      line-height: 1.5555556;
      margin-top: 0.8888889em;
      margin-bottom: 0.8888889em;
    }
    :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.3333333em;
      margin-bottom: 1.3333333em;
      padding-inline-start: 1.1111111em;
    }
    :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 2.1428571em;
      margin-top: 0;
      margin-bottom: 0.8em;
      line-height: 1.2;
    }
    :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 1.4285714em;
      margin-top: 1.6em;
      margin-bottom: 0.8em;
      line-height: 1.4;
    }
    :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 1.2857143em;
      margin-top: 1.5555556em;
      margin-bottom: 0.4444444em;
      line-height: 1.5555556;
    }
    :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.4285714em;
      margin-bottom: 0.5714286em;
      line-height: 1.4285714;
    }
    :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
    }
    :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
    }
    :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
    }
    :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.8571429em;
      border-radius: 0.3125rem;
      padding-top: 0.1428571em;
      padding-inline-end: 0.3571429em;
      padding-bottom: 0.1428571em;
      padding-inline-start: 0.3571429em;
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.8571429em;
    }
    :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.9em;
    }
    :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.8888889em;
    }
    :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.8571429em;
      line-height: 1.6666667;
      margin-top: 1.6666667em;
      margin-bottom: 1.6666667em;
      border-radius: 0.25rem;
      padding-top: 0.6666667em;
      padding-inline-end: 1em;
      padding-bottom: 0.6666667em;
      padding-inline-start: 1em;
    }
    :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
      margin-bottom: 1.1428571em;
      padding-inline-start: 1.5714286em;
    }
    :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
      margin-bottom: 1.1428571em;
      padding-inline-start: 1.5714286em;
    }
    :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.2857143em;
      margin-bottom: 0.2857143em;
    }
    :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.4285714em;
    }
    :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.4285714em;
    }
    :where(.prose-sm > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5714286em;
      margin-bottom: 0.5714286em;
    }
    :where(.prose-sm > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
    }
    :where(.prose-sm > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.1428571em;
    }
    :where(.prose-sm > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
    }
    :where(.prose-sm > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.1428571em;
    }
    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5714286em;
      margin-bottom: 0.5714286em;
    }
    :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
      margin-bottom: 1.1428571em;
    }
    :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
    }
    :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.2857143em;
      padding-inline-start: 1.5714286em;
    }
    :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2.8571429em;
      margin-bottom: 2.8571429em;
    }
    :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.8571429em;
      line-height: 1.5;
    }
    :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 1em;
      padding-bottom: 0.6666667em;
      padding-inline-start: 1em;
    }
    :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 0;
    }
    :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-top: 0.6666667em;
      padding-inline-end: 1em;
      padding-bottom: 0.6666667em;
      padding-inline-start: 1em;
    }
    :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 0;
    }
    :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
    }
    :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.8571429em;
      line-height: 1.3333333;
      margin-top: 0.6666667em;
    }
    :where(.prose-sm > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(.prose-sm > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 0;
    }
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mt-auto {
    margin-top: auto;
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .ml-12 {
    margin-left: calc(var(--spacing) * 12);
  }
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .aspect-\[2\/1\] {
    aspect-ratio: 2/1;
  }
  .aspect-\[16\/9\] {
    aspect-ratio: 16/9;
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-20 {
    height: calc(var(--spacing) * 20);
  }
  .h-24 {
    height: calc(var(--spacing) * 24);
  }
  .h-64 {
    height: calc(var(--spacing) * 64);
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }
  .max-h-\[80vh\] {
    max-height: 80vh;
  }
  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }
  .min-h-\[140px\] {
    min-height: 140px;
  }
  .min-h-\[200px\] {
    min-height: 200px;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-0 {
    width: calc(var(--spacing) * 0);
  }
  .w-0\.5 {
    width: calc(var(--spacing) * 0.5);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-40 {
    width: calc(var(--spacing) * 40);
  }
  .w-44 {
    width: calc(var(--spacing) * 44);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-80 {
    width: calc(var(--spacing) * 80);
  }
  .w-auto {
    width: auto;
  }
  .w-fit {
    width: fit-content;
  }
  .w-full {
    width: 100%;
  }
  .w-px {
    width: 1px;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-\[3rem\] {
    max-width: 3rem;
  }
  .max-w-\[4rem\] {
    max-width: 4rem;
  }
  .max-w-\[6rem\] {
    max-width: 6rem;
  }
  .max-w-\[8rem\] {
    max-width: 8rem;
  }
  .max-w-\[calc\(100vw-2rem\)\] {
    max-width: calc(100vw - 2rem);
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-none {
    max-width: none;
  }
  .max-w-sm {
    max-width: var(--container-sm);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .min-w-fit {
    min-width: fit-content;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-24 {
    --tw-translate-y: calc(var(--spacing) * 24);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .scale-95 {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .gap-0\.5 {
    gap: calc(var(--spacing) * 0.5);
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-border {
    :where(& > :not(:last-child)) {
      border-color: var(--color-border);
    }
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-r-lg {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }
  .border-none {
    --tw-border-style: none;
    border-style: none;
  }
  .border-background {
    border-color: var(--color-background);
  }
  .border-border {
    border-color: var(--color-border);
  }
  .border-border\/50 {
    border-color: var(--color-border);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-border) 50%, transparent);
    }
  }
  .border-current {
    border-color: currentcolor;
  }
  .border-muted\/30 {
    border-color: var(--color-muted);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-muted) 30%, transparent);
    }
  }
  .border-primary {
    border-color: var(--color-primary);
  }
  .border-primary\/20 {
    border-color: var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
    }
  }
  .border-white\/30 {
    border-color: color-mix(in srgb, #fff 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }
  .border-t-transparent {
    border-top-color: transparent;
  }
  .bg-accent {
    background-color: var(--color-accent);
  }
  .bg-background {
    background-color: var(--color-background);
  }
  .bg-black\/50 {
    background-color: color-mix(in srgb, #000 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }
  .bg-border {
    background-color: var(--color-border);
  }
  .bg-card {
    background-color: var(--color-card);
  }
  .bg-card\/80 {
    background-color: var(--color-card);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-card) 80%, transparent);
    }
  }
  .bg-muted {
    background-color: var(--color-muted);
  }
  .bg-muted\/20 {
    background-color: var(--color-muted);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-muted) 20%, transparent);
    }
  }
  .bg-muted\/30 {
    background-color: var(--color-muted);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-muted) 30%, transparent);
    }
  }
  .bg-muted\/50 {
    background-color: var(--color-muted);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-muted) 50%, transparent);
    }
  }
  .bg-popover\/95 {
    background-color: var(--color-popover);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-popover) 95%, transparent);
    }
  }
  .bg-primary {
    background-color: var(--color-primary);
  }
  .bg-primary\/10 {
    background-color: var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);
    }
  }
  .bg-primary\/20 {
    background-color: var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
    }
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white\/10 {
    background-color: color-mix(in srgb, #fff 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }
  .bg-white\/15 {
    background-color: color-mix(in srgb, #fff 15%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 15%, transparent);
    }
  }
  .bg-white\/20 {
    background-color: color-mix(in srgb, #fff 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }
  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-blue-500\/20 {
    --tw-gradient-from: color-mix(in srgb, oklch(62.3% 0.214 259.815) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-green-500\/20 {
    --tw-gradient-from: color-mix(in srgb, oklch(72.3% 0.219 149.579) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-green-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-orange-500\/20 {
    --tw-gradient-from: color-mix(in srgb, oklch(70.5% 0.213 47.604) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-orange-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-primary {
    --tw-gradient-from: var(--color-primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-500\/20 {
    --tw-gradient-from: color-mix(in srgb, oklch(62.7% 0.265 303.9) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-purple-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-rose-500\/20 {
    --tw-gradient-from: color-mix(in srgb, oklch(64.5% 0.246 16.439) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-rose-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-teal-500\/20 {
    --tw-gradient-from: color-mix(in srgb, oklch(70.4% 0.14 182.503) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-teal-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .via-transparent {
    --tw-gradient-via: transparent;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .to-card\/90 {
    --tw-gradient-to: var(--color-card);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-card) 90%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-cyan-500\/10 {
    --tw-gradient-to: color-mix(in srgb, oklch(71.5% 0.143 215.221) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-cyan-500) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-orange-500\/10 {
    --tw-gradient-to: color-mix(in srgb, oklch(70.5% 0.213 47.604) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-orange-500) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-pink-500\/10 {
    --tw-gradient-to: color-mix(in srgb, oklch(65.6% 0.241 354.308) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-pink-500) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-primary\/80 {
    --tw-gradient-to: var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-primary) 80%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-500\/10 {
    --tw-gradient-to: color-mix(in srgb, oklch(62.7% 0.265 303.9) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-red-500\/10 {
    --tw-gradient-to: color-mix(in srgb, oklch(63.7% 0.237 25.331) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-500) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-teal-500\/10 {
    --tw-gradient-to: color-mix(in srgb, oklch(70.4% 0.14 182.503) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-teal-500) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .object-cover {
    object-fit: cover;
  }
  .p-0\.5 {
    padding: calc(var(--spacing) * 0.5);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }
  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }
  .text-center {
    text-align: center;
  }
  .text-right {
    text-align: right;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .text-accent-foreground {
    color: var(--color-accent-foreground);
  }
  .text-foreground {
    color: var(--color-foreground);
  }
  .text-foreground\/90 {
    color: var(--color-foreground);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-foreground) 90%, transparent);
    }
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-muted-foreground {
    color: var(--color-muted-foreground);
  }
  .text-muted-foreground\/50 {
    color: var(--color-muted-foreground);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-muted-foreground) 50%, transparent);
    }
  }
  .text-muted-foreground\/70 {
    color: var(--color-muted-foreground);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-muted-foreground) 70%, transparent);
    }
  }
  .text-primary {
    color: var(--color-primary);
  }
  .text-primary-foreground {
    color: var(--color-primary-foreground);
  }
  .text-white {
    color: var(--color-white);
  }
  .italic {
    font-style: italic;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-150 {
    --tw-duration: 150ms;
    transition-duration: 150ms;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .prose-neutral {
    --tw-prose-body: oklch(37.1% 0 0);
    --tw-prose-headings: oklch(20.5% 0 0);
    --tw-prose-lead: oklch(43.9% 0 0);
    --tw-prose-links: oklch(20.5% 0 0);
    --tw-prose-bold: oklch(20.5% 0 0);
    --tw-prose-counters: oklch(55.6% 0 0);
    --tw-prose-bullets: oklch(87% 0 0);
    --tw-prose-hr: oklch(92.2% 0 0);
    --tw-prose-quotes: oklch(20.5% 0 0);
    --tw-prose-quote-borders: oklch(92.2% 0 0);
    --tw-prose-captions: oklch(55.6% 0 0);
    --tw-prose-kbd: oklch(20.5% 0 0);
    --tw-prose-kbd-shadows: NaN NaN NaN;
    --tw-prose-code: oklch(20.5% 0 0);
    --tw-prose-pre-code: oklch(92.2% 0 0);
    --tw-prose-pre-bg: oklch(26.9% 0 0);
    --tw-prose-th-borders: oklch(87% 0 0);
    --tw-prose-td-borders: oklch(92.2% 0 0);
    --tw-prose-invert-body: oklch(87% 0 0);
    --tw-prose-invert-headings: #fff;
    --tw-prose-invert-lead: oklch(70.8% 0 0);
    --tw-prose-invert-links: #fff;
    --tw-prose-invert-bold: #fff;
    --tw-prose-invert-counters: oklch(70.8% 0 0);
    --tw-prose-invert-bullets: oklch(43.9% 0 0);
    --tw-prose-invert-hr: oklch(37.1% 0 0);
    --tw-prose-invert-quotes: oklch(97% 0 0);
    --tw-prose-invert-quote-borders: oklch(37.1% 0 0);
    --tw-prose-invert-captions: oklch(70.8% 0 0);
    --tw-prose-invert-kbd: #fff;
    --tw-prose-invert-kbd-shadows: 255 255 255;
    --tw-prose-invert-code: #fff;
    --tw-prose-invert-pre-code: oklch(87% 0 0);
    --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
    --tw-prose-invert-th-borders: oklch(43.9% 0 0);
    --tw-prose-invert-td-borders: oklch(37.1% 0 0);
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .toc-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--color-border) transparent;
  }
  .group-hover\:scale-105 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:text-primary {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-primary);
      }
    }
  }
  .group-hover\:opacity-90 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 90%;
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .placeholder\:text-muted-foreground {
    &::placeholder {
      color: var(--color-muted-foreground);
    }
  }
  .hover\:translate-x-1 {
    &:hover {
      @media (hover: hover) {
        --tw-translate-x: calc(var(--spacing) * 1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .hover\:-translate-y-0\.5 {
    &:hover {
      @media (hover: hover) {
        --tw-translate-y: calc(var(--spacing) * -0.5);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .hover\:-translate-y-1 {
    &:hover {
      @media (hover: hover) {
        --tw-translate-y: calc(var(--spacing) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .hover\:scale-105 {
    &:hover {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\:scale-110 {
    &:hover {
      @media (hover: hover) {
        --tw-scale-x: 110%;
        --tw-scale-y: 110%;
        --tw-scale-z: 110%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\:scale-\[1\.02\] {
    &:hover {
      @media (hover: hover) {
        scale: 1.02;
      }
    }
  }
  .hover\:border-primary\/20 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-primary);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
        }
      }
    }
  }
  .hover\:border-primary\/30 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-primary);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-primary) 30%, transparent);
        }
      }
    }
  }
  .hover\:border-primary\/50 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-primary);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-primary) 50%, transparent);
        }
      }
    }
  }
  .hover\:bg-accent {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-accent);
      }
    }
  }
  .hover\:bg-accent\/50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-accent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-accent) 50%, transparent);
        }
      }
    }
  }
  .hover\:bg-muted\/40 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-muted);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-muted) 40%, transparent);
        }
      }
    }
  }
  .hover\:bg-muted\/50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-muted);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-muted) 50%, transparent);
        }
      }
    }
  }
  .hover\:bg-primary\/5 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-primary) 5%, transparent);
        }
      }
    }
  }
  .hover\:bg-primary\/10 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);
        }
      }
    }
  }
  .hover\:bg-primary\/20 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
        }
      }
    }
  }
  .hover\:bg-primary\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);
        }
      }
    }
  }
  .hover\:text-accent-foreground {
    &:hover {
      @media (hover: hover) {
        color: var(--color-accent-foreground);
      }
    }
  }
  .hover\:text-foreground {
    &:hover {
      @media (hover: hover) {
        color: var(--color-foreground);
      }
    }
  }
  .hover\:text-primary {
    &:hover {
      @media (hover: hover) {
        color: var(--color-primary);
      }
    }
  }
  .hover\:text-primary\/80 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-primary);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-primary) 80%, transparent);
        }
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-md {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .focus\:bg-accent {
    &:focus {
      background-color: var(--color-accent);
    }
  }
  .focus\:text-accent-foreground {
    &:focus {
      color: var(--color-accent-foreground);
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-primary\/20 {
    &:focus {
      --tw-ring-color: var(--color-primary);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
      }
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .active\:scale-95 {
    &:active {
      --tw-scale-x: 95%;
      --tw-scale-y: 95%;
      --tw-scale-z: 95%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .sm\:inset-auto {
    @media (width >= 40rem) {
      inset: auto;
    }
  }
  .sm\:top-1\/2 {
    @media (width >= 40rem) {
      top: calc(1/2 * 100%);
    }
  }
  .sm\:right-0 {
    @media (width >= 40rem) {
      right: calc(var(--spacing) * 0);
    }
  }
  .sm\:left-0 {
    @media (width >= 40rem) {
      left: calc(var(--spacing) * 0);
    }
  }
  .sm\:left-1\/2 {
    @media (width >= 40rem) {
      left: calc(1/2 * 100%);
    }
  }
  .sm\:mx-2 {
    @media (width >= 40rem) {
      margin-inline: calc(var(--spacing) * 2);
    }
  }
  .sm\:mx-auto {
    @media (width >= 40rem) {
      margin-inline: auto;
    }
  }
  .sm\:block {
    @media (width >= 40rem) {
      display: block;
    }
  }
  .sm\:inline {
    @media (width >= 40rem) {
      display: inline;
    }
  }
  .sm\:w-full {
    @media (width >= 40rem) {
      width: 100%;
    }
  }
  .sm\:max-w-md {
    @media (width >= 40rem) {
      max-width: var(--container-md);
    }
  }
  .sm\:-translate-x-1\/2 {
    @media (width >= 40rem) {
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .sm\:translate-x-0 {
    @media (width >= 40rem) {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .sm\:-translate-y-1\/2 {
    @media (width >= 40rem) {
      --tw-translate-y: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .sm\:px-3 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 3);
    }
  }
  .sm\:px-4 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .sm\:py-2 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 2);
    }
  }
  .sm\:py-3 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 3);
    }
  }
  .md\:mb-6 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:inline {
    @media (width >= 48rem) {
      display: inline;
    }
  }
  .md\:h-80 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 80);
    }
  }
  .md\:max-w-\[12rem\] {
    @media (width >= 48rem) {
      max-width: 12rem;
    }
  }
  .md\:max-w-\[16rem\] {
    @media (width >= 48rem) {
      max-width: 16rem;
    }
  }
  .md\:max-w-none {
    @media (width >= 48rem) {
      max-width: none;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:items-start {
    @media (width >= 48rem) {
      align-items: flex-start;
    }
  }
  .md\:justify-start {
    @media (width >= 48rem) {
      justify-content: flex-start;
    }
  }
  .md\:gap-1 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 1);
    }
  }
  .md\:gap-2 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 2);
    }
  }
  .md\:gap-4 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .md\:space-x-2 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .md\:px-3 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 3);
    }
  }
  .md\:py-1\.5 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 1.5);
    }
  }
  .md\:pr-72 {
    @media (width >= 48rem) {
      padding-right: calc(var(--spacing) * 72);
    }
  }
  .md\:text-left {
    @media (width >= 48rem) {
      text-align: left;
    }
  }
  .md\:text-4xl {
    @media (width >= 48rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .lg\:max-w-none {
    @media (width >= 64rem) {
      max-width: none;
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .dark\:prose-invert {
    &:where(.dark, .dark *) {
      --tw-prose-body: var(--tw-prose-invert-body);
      --tw-prose-headings: var(--tw-prose-invert-headings);
      --tw-prose-lead: var(--tw-prose-invert-lead);
      --tw-prose-links: var(--tw-prose-invert-links);
      --tw-prose-bold: var(--tw-prose-invert-bold);
      --tw-prose-counters: var(--tw-prose-invert-counters);
      --tw-prose-bullets: var(--tw-prose-invert-bullets);
      --tw-prose-hr: var(--tw-prose-invert-hr);
      --tw-prose-quotes: var(--tw-prose-invert-quotes);
      --tw-prose-quote-borders: var(--tw-prose-invert-quote-borders);
      --tw-prose-captions: var(--tw-prose-invert-captions);
      --tw-prose-kbd: var(--tw-prose-invert-kbd);
      --tw-prose-kbd-shadows: var(--tw-prose-invert-kbd-shadows);
      --tw-prose-code: var(--tw-prose-invert-code);
      --tw-prose-pre-code: var(--tw-prose-invert-pre-code);
      --tw-prose-pre-bg: var(--tw-prose-invert-pre-bg);
      --tw-prose-th-borders: var(--tw-prose-invert-th-borders);
      --tw-prose-td-borders: var(--tw-prose-invert-td-borders);
    }
  }
}
:root {
  --color-primary: oklch(0.55 0.15 252.5);
  --color-primary-foreground: oklch(0.985 0 0);
  --color-secondary: oklch(0.97 0 0);
  --color-secondary-foreground: oklch(0.205 0 0);
  --color-accent: oklch(0.97 0 0);
  --color-accent-foreground: oklch(0.205 0 0);
  --color-background: oklch(1 0 0);
  --color-foreground: oklch(0.145 0 0);
  --color-muted: oklch(0.97 0 0);
  --color-muted-foreground: oklch(0.556 0 0);
  --color-border: oklch(0.922 0 0);
  --color-card: oklch(1 0 0);
  --color-card-foreground: oklch(0.145 0 0);
  --color-popover: oklch(1 0 0);
  --color-popover-foreground: oklch(0.145 0 0);
  --color-note: oklch(0.61 0.15 252.5);
  --color-tip: oklch(0.61 0.15 162.48);
  --color-important: oklch(0.61 0.15 39.04);
  --color-warning: oklch(0.61 0.15 85.87);
  --color-caution: oklch(0.61 0.15 12.57);
}
.dark {
  --color-primary: oklch(0.65 0.15 252.5);
  --color-primary-foreground: oklch(0.985 0 0);
  --color-secondary: oklch(0.269 0 0);
  --color-secondary-foreground: oklch(0.985 0 0);
  --color-accent: oklch(0.371 0 0);
  --color-accent-foreground: oklch(0.985 0 0);
  --color-background: oklch(0.145 0 0);
  --color-foreground: oklch(0.985 0 0);
  --color-muted: oklch(0.269 0 0);
  --color-muted-foreground: oklch(0.708 0 0);
  --color-border: oklch(1 0 0 / 10%);
  --color-card: oklch(0.205 0 0);
  --color-card-foreground: oklch(0.985 0 0);
  --color-popover: oklch(0.269 0 0);
  --color-popover-foreground: oklch(0.985 0 0);
  --color-note: oklch(0.67 0.15 252.5);
  --color-tip: oklch(0.67 0.15 162.48);
  --color-important: oklch(0.67 0.15 39.04);
  --color-warning: oklch(0.67 0.15 85.87);
  --color-caution: oklch(0.67 0.15 12.57);
}
[data-theme="claude"] {
  --color-primary: oklch(0.62 0.14 39.04);
  --color-primary-foreground: oklch(1 0 0);
  --color-secondary: oklch(0.92 0.01 92.99);
  --color-secondary: oklch(0.92 0.01 92.99);
  --color-secondary-foreground: oklch(0.43 0.02 98.6);
  --color-accent: oklch(0.92 0.01 92.99);
  --color-accent-foreground: oklch(0.27 0.02 98.94);
  --color-background: oklch(0.98 0.01 95.1);
  --color-foreground: oklch(0.34 0.03 95.72);
  --color-muted: oklch(0.93 0.02 90.24);
  --color-muted-foreground: oklch(0.61 0.01 97.42);
  --color-border: oklch(0.88 0.01 97.36);
  --color-card: oklch(0.98 0.01 95.1);
  --color-card-foreground: oklch(0.19 0 106.59);
  --color-popover: oklch(1 0 0);
  --color-popover-foreground: oklch(0.27 0.02 98.94);
  --color-note: oklch(0.58 0.13 240.0);
  --color-tip: oklch(0.60 0.12 150.0);
  --color-important: oklch(0.62 0.14 39.04);
  --color-warning: oklch(0.63 0.14 70.0);
  --color-caution: oklch(0.58 0.15 25.0);
}
[data-theme="claude"].dark {
  --color-primary: oklch(0.67 0.13 38.76);
  --color-primary-foreground: oklch(1 0 0);
  --color-secondary: oklch(0.98 0.01 95.1);
  --color-secondary-foreground: oklch(0.31 0 106.6);
  --color-accent: oklch(0.21 0.01 95.42);
  --color-accent-foreground: oklch(0.97 0.01 98.88);
  --color-background: oklch(0.27 0 106.64);
  --color-foreground: oklch(0.81 0.01 93.01);
  --color-muted: oklch(0.22 0 106.71);
  --color-muted-foreground: oklch(0.77 0.02 99.07);
  --color-border: oklch(0.36 0.01 106.89);
  --color-card: oklch(0.27 0 106.64);
  --color-card-foreground: oklch(0.98 0.01 95.1);
  --color-popover: oklch(0.31 0 106.6);
  --color-popover-foreground: oklch(0.92 0 106.48);
  --color-note: oklch(0.65 0.12 235.0);
  --color-tip: oklch(0.68 0.11 145.0);
  --color-important: oklch(0.67 0.13 38.76);
  --color-warning: oklch(0.70 0.13 68.0);
  --color-caution: oklch(0.65 0.14 20.0);
}
[data-theme="bumblebee"] {
  --color-primary: oklch(0.85 0.199 91.936);
  --color-primary-foreground: oklch(0.42 0.095 57.708);
  --color-secondary: oklch(0.75 0.183 55.934);
  --color-secondary-foreground: oklch(0.40 0.123 38.172);
  --color-accent: oklch(0 0 0);
  --color-accent-foreground: oklch(1 0 0);
  --color-background: oklch(1 0 0);
  --color-foreground: oklch(0.20 0 0);
  --color-muted: oklch(0.97 0 0);
  --color-muted-foreground: oklch(0.37 0.01 67.558);
  --color-border: oklch(0.92 0 0);
  --color-card: oklch(1 0 0);
  --color-card-foreground: oklch(0.20 0 0);
  --color-popover: oklch(1 0 0);
  --color-popover-foreground: oklch(0.20 0 0);
  --color-note: oklch(0.74 0.16 232.661);
  --color-tip: oklch(0.76 0.177 163.223);
  --color-important: oklch(0.85 0.199 91.936);
  --color-warning: oklch(0.82 0.189 84.429);
  --color-caution: oklch(0.70 0.191 22.216);
}
[data-theme="bumblebee"].dark {
  --color-primary: oklch(0.70 0.16 91.936);
  --color-primary-foreground: oklch(0.95 0 0);
  --color-secondary: oklch(0.45 0.15 55.934);
  --color-secondary-foreground: oklch(0.95 0 0);
  --color-accent: oklch(0.15 0 0);
  --color-accent-foreground: oklch(0.95 0 0);
  --color-background: oklch(0.12 0 0);
  --color-foreground: oklch(0.92 0.003 48.717);
  --color-muted: oklch(0.18 0 0);
  --color-muted-foreground: oklch(0.65 0.01 67.558);
  --color-border: oklch(0.25 0 0);
  --color-card: oklch(0.15 0 0);
  --color-card-foreground: oklch(0.92 0.003 48.717);
  --color-popover: oklch(0.18 0 0);
  --color-popover-foreground: oklch(0.92 0.003 48.717);
  --color-note: oklch(0.65 0.14 235.0);
  --color-tip: oklch(0.68 0.15 160.0);
  --color-important: oklch(0.70 0.16 91.936);
  --color-warning: oklch(0.75 0.16 82.0);
  --color-caution: oklch(0.65 0.16 25.0);
}
[data-theme="emerald"] {
  --color-primary: oklch(0.767 0.135 153.45);
  --color-primary-foreground: oklch(0.334 0.04 162.24);
  --color-secondary: oklch(0.613 0.202 261.294);
  --color-secondary-foreground: oklch(1 0 0);
  --color-accent: oklch(0.728 0.149 33.2);
  --color-accent-foreground: oklch(0 0 0);
  --color-background: oklch(1 0 0);
  --color-foreground: oklch(0.355 0.032 262.988);
  --color-muted: oklch(0.93 0 0);
  --color-muted-foreground: oklch(0.355 0.032 262.988);
  --color-border: oklch(0.86 0 0);
  --color-card: oklch(1 0 0);
  --color-card-foreground: oklch(0.355 0.032 262.988);
  --color-popover: oklch(1 0 0);
  --color-popover-foreground: oklch(0.355 0.032 262.988);
  --color-note: oklch(0.721 0.191 231.6);
  --color-tip: oklch(0.648 0.15 160);
  --color-important: oklch(0.767 0.135 153.45);
  --color-warning: oklch(0.847 0.199 83.87);
  --color-caution: oklch(0.718 0.221 22.18);
}
[data-theme="emerald"].dark {
  --color-primary: oklch(0.65 0.12 153.45);
  --color-primary-foreground: oklch(0.95 0 0);
  --color-secondary: oklch(0.45 0.16 261.294);
  --color-secondary-foreground: oklch(0.95 0 0);
  --color-accent: oklch(0.55 0.12 33.2);
  --color-accent-foreground: oklch(0.95 0 0);
  --color-background: oklch(0.12 0 0);
  --color-foreground: oklch(0.985 0.001 247.838);
  --color-muted: oklch(0.18 0 0);
  --color-muted-foreground: oklch(0.65 0.02 262.988);
  --color-border: oklch(0.25 0 0);
  --color-card: oklch(0.15 0 0);
  --color-card-foreground: oklch(0.985 0.001 247.838);
  --color-popover: oklch(0.18 0 0);
  --color-popover-foreground: oklch(0.985 0.001 247.838);
  --color-note: oklch(0.65 0.16 235.0);
  --color-tip: oklch(0.70 0.13 158.0);
  --color-important: oklch(0.65 0.12 153.45);
  --color-warning: oklch(0.75 0.16 82.0);
  --color-caution: oklch(0.65 0.18 25.0);
}
[data-theme="nord"] {
  --color-primary: oklch(0.594 0.077 254.027);
  --color-primary-foreground: oklch(0.119 0.015 254.027);
  --color-secondary: oklch(0.697 0.059 248.687);
  --color-secondary-foreground: oklch(0.139 0.011 248.687);
  --color-accent: oklch(0.775 0.062 217.469);
  --color-accent-foreground: oklch(0.155 0.012 217.469);
  --color-background: oklch(0.951 0.007 260.731);
  --color-foreground: oklch(0.324 0.022 264.182);
  --color-muted: oklch(0.933 0.01 261.788);
  --color-muted-foreground: oklch(0.452 0.035 264.131);
  --color-border: oklch(0.899 0.016 262.749);
  --color-card: oklch(0.951 0.007 260.731);
  --color-card-foreground: oklch(0.324 0.022 264.182);
  --color-popover: oklch(0.951 0.007 260.731);
  --color-popover-foreground: oklch(0.324 0.022 264.182);
  --color-note: oklch(0.692 0.062 332.664);
  --color-tip: oklch(0.768 0.074 131.063);
  --color-important: oklch(0.594 0.077 254.027);
  --color-warning: oklch(0.855 0.089 84.093);
  --color-caution: oklch(0.606 0.12 15.341);
}
[data-theme="nord"].dark {
  --color-primary: oklch(0.55 0.07 254.027);
  --color-primary-foreground: oklch(0.95 0 0);
  --color-secondary: oklch(0.55 0.05 248.687);
  --color-secondary-foreground: oklch(0.95 0 0);
  --color-accent: oklch(0.65 0.05 217.469);
  --color-accent-foreground: oklch(0.95 0 0);
  --color-background: oklch(0.18 0.015 264.182);
  --color-foreground: oklch(0.899 0.016 262.749);
  --color-muted: oklch(0.25 0.02 264.131);
  --color-muted-foreground: oklch(0.70 0.035 264.131);
  --color-border: oklch(0.35 0.025 264.182);
  --color-card: oklch(0.22 0.018 264.182);
  --color-card-foreground: oklch(0.899 0.016 262.749);
  --color-popover: oklch(0.25 0.02 264.131);
  --color-popover-foreground: oklch(0.899 0.016 262.749);
  --color-note: oklch(0.65 0.055 330.0);
  --color-tip: oklch(0.70 0.065 128.0);
  --color-important: oklch(0.55 0.07 254.027);
  --color-warning: oklch(0.75 0.08 82.0);
  --color-caution: oklch(0.60 0.11 18.0);
}
[data-theme="sunset"] {
  --color-primary: oklch(0.747 0.158 39.947);
  --color-primary-foreground: oklch(0.149 0.031 39.947);
  --color-secondary: oklch(0.725 0.177 2.72);
  --color-secondary-foreground: oklch(0.145 0.035 2.72);
  --color-accent: oklch(0.713 0.166 299.844);
  --color-accent-foreground: oklch(0.143 0.033 299.844);
  --color-background: oklch(0.98 0.01 40);
  --color-foreground: oklch(0.22 0.019 237.69);
  --color-muted: oklch(0.94 0.015 40);
  --color-muted-foreground: oklch(0.45 0.025 237.69);
  --color-border: oklch(0.88 0.02 40);
  --color-card: oklch(0.98 0.01 40);
  --color-card-foreground: oklch(0.22 0.019 237.69);
  --color-popover: oklch(0.99 0.005 40);
  --color-popover-foreground: oklch(0.22 0.019 237.69);
  --color-note: oklch(0.65 0.15 240);
  --color-tip: oklch(0.70 0.14 160);
  --color-important: oklch(0.747 0.158 39.947);
  --color-warning: oklch(0.75 0.16 85);
  --color-caution: oklch(0.65 0.18 15);
}
[data-theme="sunset"].dark {
  --color-primary: oklch(0.747 0.158 39.947);
  --color-primary-foreground: oklch(0.149 0.031 39.947);
  --color-secondary: oklch(0.725 0.177 2.72);
  --color-secondary-foreground: oklch(0.145 0.035 2.72);
  --color-accent: oklch(0.713 0.166 299.844);
  --color-accent-foreground: oklch(0.143 0.033 299.844);
  --color-background: oklch(0.22 0.019 237.69);
  --color-foreground: oklch(0.774 0.043 245.096);
  --color-muted: oklch(0.26 0.019 237.69);
  --color-muted-foreground: oklch(0.70 0.019 237.69);
  --color-border: oklch(0.35 0.025 237.69);
  --color-card: oklch(0.24 0.019 237.69);
  --color-card-foreground: oklch(0.774 0.043 245.096);
  --color-popover: oklch(0.26 0.019 237.69);
  --color-popover-foreground: oklch(0.774 0.043 245.096);
  --color-note: oklch(0.70 0.14 240);
  --color-tip: oklch(0.75 0.13 160);
  --color-important: oklch(0.747 0.158 39.947);
  --color-warning: oklch(0.80 0.15 85);
  --color-caution: oklch(0.70 0.17 15);
}
[data-theme="abyss"] {
  --color-primary: oklch(0.75 0.20 125);
  --color-primary-foreground: oklch(0.15 0.15 125);
  --color-secondary: oklch(0.70 0.12 298.3);
  --color-secondary-foreground: oklch(0.20 0.08 298.3);
  --color-accent: oklch(0.65 0.15 209);
  --color-accent-foreground: oklch(0.98 0 0);
  --color-background: oklch(0.96 0.02 209);
  --color-foreground: oklch(0.25 0.08 209);
  --color-muted: oklch(0.92 0.03 209);
  --color-muted-foreground: oklch(0.45 0.06 209);
  --color-border: oklch(0.85 0.04 209);
  --color-card: oklch(0.96 0.02 209);
  --color-card-foreground: oklch(0.25 0.08 209);
  --color-popover: oklch(0.98 0.01 209);
  --color-popover-foreground: oklch(0.25 0.08 209);
  --color-note: oklch(0.65 0.15 240);
  --color-tip: oklch(0.75 0.20 125);
  --color-important: oklch(0.75 0.20 125);
  --color-warning: oklch(0.75 0.16 85);
  --color-caution: oklch(0.65 0.18 15);
}
[data-theme="abyss"].dark {
  --color-primary: oklch(0.92 0.2653 125);
  --color-primary-foreground: oklch(0.50 0.2653 125);
  --color-secondary: oklch(0.833 0.0764 298.3);
  --color-secondary-foreground: oklch(0.433 0.0764 298.3);
  --color-accent: oklch(0.43 0 0);
  --color-accent-foreground: oklch(0.98 0 0);
  --color-background: oklch(0.20 0.08 209);
  --color-foreground: oklch(0.90 0.076 70.697);
  --color-muted: oklch(0.30 0.08 209);
  --color-muted-foreground: oklch(0.70 0.06 209);
  --color-border: oklch(0.35 0.08 209);
  --color-card: oklch(0.22 0.08 209);
  --color-card-foreground: oklch(0.90 0.076 70.697);
  --color-popover: oklch(0.30 0.08 209);
  --color-popover-foreground: oklch(0.90 0.076 70.697);
  --color-note: oklch(0.70 0.14 240);
  --color-tip: oklch(0.80 0.22 125);
  --color-important: oklch(0.92 0.2653 125);
  --color-warning: oklch(0.80 0.15 85);
  --color-caution: oklch(0.70 0.17 15);
}
[data-theme="dracula"] {
  --color-primary: oklch(0.70 0.15 346.812);
  --color-primary-foreground: oklch(0.98 0.007 106.545);
  --color-secondary: oklch(0.68 0.12 301.883);
  --color-secondary-foreground: oklch(0.98 0.007 106.545);
  --color-accent: oklch(0.75 0.10 66.558);
  --color-accent-foreground: oklch(0.20 0.024 66.558);
  --color-background: oklch(0.95 0.01 277.508);
  --color-foreground: oklch(0.30 0.022 277.508);
  --color-muted: oklch(0.90 0.015 277.508);
  --color-muted-foreground: oklch(0.50 0.02 277.508);
  --color-border: oklch(0.82 0.02 277.508);
  --color-card: oklch(0.95 0.01 277.508);
  --color-card-foreground: oklch(0.30 0.022 277.508);
  --color-popover: oklch(0.97 0.005 277.508);
  --color-popover-foreground: oklch(0.30 0.022 277.508);
  --color-note: oklch(0.65 0.15 240);
  --color-tip: oklch(0.70 0.14 160);
  --color-important: oklch(0.70 0.15 346.812);
  --color-warning: oklch(0.75 0.16 85);
  --color-caution: oklch(0.65 0.18 15);
}
[data-theme="dracula"].dark {
  --color-primary: oklch(0.755 0.183 346.812);
  --color-primary-foreground: oklch(0.151 0.036 346.812);
  --color-secondary: oklch(0.742 0.148 301.883);
  --color-secondary-foreground: oklch(0.148 0.029 301.883);
  --color-accent: oklch(0.834 0.124 66.558);
  --color-accent-foreground: oklch(0.167 0.024 66.558);
  --color-background: oklch(0.288 0.022 277.508);
  --color-foreground: oklch(0.977 0.007 106.545);
  --color-muted: oklch(0.394 0.032 275.524);
  --color-muted-foreground: oklch(0.879 0.006 275.524);
  --color-border: oklch(0.45 0.035 277.508);
  --color-card: oklch(0.32 0.025 277.508);
  --color-card-foreground: oklch(0.977 0.007 106.545);
  --color-popover: oklch(0.394 0.032 275.524);
  --color-popover-foreground: oklch(0.977 0.007 106.545);
  --color-note: oklch(0.70 0.14 240);
  --color-tip: oklch(0.75 0.13 160);
  --color-important: oklch(0.755 0.183 346.812);
  --color-warning: oklch(0.80 0.15 85);
  --color-caution: oklch(0.70 0.17 15);
}
@layer utilities {
  .prose {
    --tw-prose-body: var(--color-foreground);
    --tw-prose-headings: var(--color-foreground);
    --tw-prose-lead: var(--color-muted-foreground);
    --tw-prose-links: var(--color-primary);
    --tw-prose-bold: var(--color-foreground);
    --tw-prose-counters: var(--color-muted-foreground);
    --tw-prose-bullets: var(--color-muted-foreground);
    --tw-prose-hr: var(--color-border);
    --tw-prose-quotes: var(--color-foreground);
    --tw-prose-quote-borders: var(--color-border);
    --tw-prose-captions: var(--color-muted-foreground);
    --tw-prose-code: var(--color-foreground);
    --tw-prose-pre-code: var(--color-muted-foreground);
    --tw-prose-pre-bg: var(--color-muted);
    --tw-prose-th-borders: var(--color-border);
    --tw-prose-td-borders: var(--color-border);
    font-family: ui-sans-serif, system-ui, sans-serif;
    line-height: 1.7;
    font-size: 1rem;
    max-width: none;
  }
  .prose h1 {
    scroll-margin: calc(var(--spacing) * 20);
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
    @media (width >= 64rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--tw-prose-headings);
    position: relative;
  }
  .prose h1::after {
    content: "";
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 3rem;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary), transparent);
    border-radius: 2px;
  }
  .prose h2 {
    scroll-margin: calc(var(--spacing) * 20);
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--tw-prose-headings);
    position: relative;
    padding-left: 1rem;
  }
  .prose h2::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0.25rem;
    width: 4px;
    height: 1.5rem;
    background: var(--color-primary);
    border-radius: 2px;
  }
  .prose h3 {
    scroll-margin: calc(var(--spacing) * 20);
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--tw-prose-headings);
  }
  .prose h4 {
    scroll-margin: calc(var(--spacing) * 20);
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--tw-prose-headings);
  }
  .prose h4[id^="alert-"] {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }
  .prose h5 {
    scroll-margin: calc(var(--spacing) * 20);
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
    margin-top: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--tw-prose-headings);
  }
  .prose h6 {
    scroll-margin: calc(var(--spacing) * 20);
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    color: var(--tw-prose-headings);
  }
  .prose p {
    --tw-leading: calc(var(--spacing) * 7);
    line-height: calc(var(--spacing) * 7);
    margin-top: 1.25rem;
    margin-bottom: 1.25rem;
    color: var(--tw-prose-body);
  }
  .prose p:first-child {
    margin-top: 0;
  }
  .prose p:last-child {
    margin-bottom: 0;
  }
  .prose blockquote {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
    padding-left: calc(var(--spacing) * 6);
    font-style: italic;
    border-left-color: var(--color-primary);
    background: var(--color-muted);
    @supports (color: color-mix(in lab, red, red)) {
      background: color-mix(in srgb, var(--color-muted) 30%, transparent);
    }
    margin: 1.5rem 0;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    position: relative;
  }
  .prose blockquote::before {
    content: '"';
    font-size: 4rem;
    color: var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in srgb, var(--color-primary) 20%, transparent);
    }
    position: absolute;
    top: -0.5rem;
    left: 1rem;
    font-family: serif;
    line-height: 1;
  }
  .prose blockquote p {
    margin: 0;
    font-style: italic;
    font-size: 1.1rem;
    color: var(--tw-prose-quotes);
  }
  .prose blockquote blockquote {
    margin: 1rem 0;
    border-left-color: var(--color-accent);
    background: var(--color-accent);
    @supports (color: color-mix(in lab, red, red)) {
      background: color-mix(in srgb, var(--color-accent) 10%, transparent);
    }
  }
  .prose blockquote blockquote::before {
    color: var(--color-accent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in srgb, var(--color-accent) 20%, transparent);
    }
  }
  .prose .footnote-ref {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    vertical-align: super;
    line-height: 1;
  }
  .prose .footnote-ref:hover {
    color: var(--color-accent);
    text-decoration: underline;
  }
  .prose .footnotes {
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--color-border);
    font-size: 0.875rem;
  }
  .prose .footnotes ol {
    padding-left: 1rem;
  }
  .prose .footnotes li {
    margin: 0.5rem 0;
  }
  .prose .footnote-backref {
    color: var(--color-primary);
    text-decoration: none;
    margin-left: 0.5rem;
  }
  .prose .footnote-backref:hover {
    color: var(--color-accent);
    text-decoration: underline;
  }
  .prose a {
    color: var(--tw-prose-links);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
    background: linear-gradient( to bottom, transparent 50%, var(--color-primary) 50% );
    @supports (color: color-mix(in lab, red, red)) {
      background: linear-gradient( to bottom, transparent 50%, color-mix(in srgb, var(--color-primary) 15%, transparent) 50% );
    }
    background-size: 100% 200%;
    background-position: 0 0;
  }
  .prose a::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--color-primary);
    transition: width 0.3s ease;
  }
  .prose a:hover {
    color: var(--color-primary);
    background-position: 0 100%;
  }
  .prose a:hover::after {
    width: 100%;
  }
  .prose ul {
    list-style: none;
    padding-left: 0;
    margin: 1.25rem 0;
  }
  .prose ul li {
    position: relative;
    padding-left: 1rem;
    margin: 0.5rem 0.6rem;
    color: var(--tw-prose-body);
  }
  .prose ul li::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0.75rem;
    width: 6px;
    height: 6px;
    background: var(--color-primary);
    border-radius: 50%;
  }
  .prose ol {
    counter-reset: item;
    padding-left: 0;
    margin: 1.25rem 0;
    list-style: none;
  }
  .prose ol li {
    position: relative;
    padding-left: 2rem;
    margin: 0.5rem 0.6rem;
    counter-increment: item;
    color: var(--tw-prose-body);
  }
  .prose ol li::before {
    content: counter(item);
    position: absolute;
    left: 0;
    top: 0.2rem;
    background: var(--color-primary);
    color: white;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    line-height: 1;
  }
  .prose ul li.task-list-item {
    list-style: none;
    position: relative;
    padding-left: 2rem;
    margin: 0.5rem 0;
  }
  .prose ul li.task-list-item::before {
    display: none;
  }
  .prose ul li.task-list-item input[type="checkbox"] {
    position: absolute;
    left: 0;
    top: 0.5rem;
    width: 1rem;
    height: 1rem;
    margin: 0;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    border: 2px solid var(--color-border);
    border-radius: 0.25rem;
    background: var(--color-background);
    transition: all 0.2s ease;
  }
  .prose ul li.task-list-item input[type="checkbox"]:checked {
    background: var(--color-primary);
    border-color: var(--color-primary);
  }
  .prose ul li.task-list-item input[type="checkbox"]:checked::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
  }
  .prose ul li.task-list-item input[type="checkbox"]:hover {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 0 0 2px color-mix(in srgb, var(--color-primary) 20%, transparent);
    }
  }
  .prose dl {
    margin: 1.5rem 0;
  }
  .prose dt {
    font-weight: 600;
    color: var(--tw-prose-headings);
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1rem;
  }
  .prose dt::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0.5rem;
    width: 4px;
    height: 4px;
    background: var(--color-primary);
    border-radius: 50%;
  }
  .prose dd {
    margin-left: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--tw-prose-body);
    padding-left: 1rem;
    border-left: 2px solid var(--color-border);
    @supports (color: color-mix(in lab, red, red)) {
      border-left: 2px solid color-mix(in srgb, var(--color-border) 50%, transparent);
    }
  }
  .prose ul ul, .prose ol ol, .prose ul ol, .prose ol ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
  }
  .prose ul ul li::before {
    width: 4px;
    height: 4px;
    border-radius: 0;
    background: var(--color-accent);
  }
  .prose ul ul ul li::before {
    content: "";
    width: 0;
    height: 0;
    border-left: 3px solid var(--color-accent);
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    border-radius: 0;
    background: transparent;
    top: 0.8rem;
  }
  .prose ol ol li::before {
    background: var(--color-accent);
    font-size: 0.7rem;
    width: 1.1rem;
    height: 1.1rem;
  }
  .prose ol ol ol li::before {
    background: var(--color-muted-foreground);
    font-size: 0.65rem;
    width: 1rem;
    height: 1rem;
  }
  .prose code {
    border-radius: 0.25rem;
    background-color: var(--color-muted);
    padding-inline: calc(var(--spacing) * 1.5);
    padding-block: calc(var(--spacing) * 0.5);
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    color: var(--tw-prose-code);
    font-weight: 600;
  }
  .code-block-container {
    margin: 1.5rem 0;
  }
  .code-block-content .chroma {
    overflow-x: auto;
    padding: calc(var(--spacing) * 4);
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    margin: 0;
    border: none;
    border-radius: 0;
    background: transparent !important;
  }
  .code-block-content .chroma pre {
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    overflow: visible;
  }
  .code-block-content .chroma code {
    background-color: transparent;
    padding: calc(var(--spacing) * 0);
    font-weight: normal;
    font-family: inherit;
    background: transparent;
  }
  .code-block-content .chroma .ln {
    user-select: none;
    margin-right: 0.4em;
    padding: 0 0.4em 0 0.4em;
    color: var(--chroma-ln, #7f7f7f);
  }
  .code-block-content .chroma .line {
    display: flex;
  }
  .code-block-content .chroma .cl {
    flex: 1;
  }
  .collapse-overlay {
    cursor: pointer;
    transition: opacity 0.3s ease;
  }
  .collapse-overlay:hover {
    backdrop-filter: blur(1px);
  }
  .collapse-overlay > div {
    transition: transform 0.2s ease;
  }
  .collapse-overlay:hover > div {
    transform: translateX(-50%) scale(1.05);
  }
  .code-block-content {
    transition: max-height 0.3s ease-out;
  }
  .prose code:not(.chroma code) {
    border-radius: 0.25rem;
    background-color: var(--color-muted);
    padding-inline: calc(var(--spacing) * 1.5);
    padding-block: calc(var(--spacing) * 0.5);
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    color: var(--tw-prose-code);
    font-weight: 600;
  }
  .prose pre:not(.chroma) {
    overflow-x: auto;
    padding: calc(var(--spacing) * 4);
    background: var(--tw-prose-pre-bg);
    margin: 0;
    position: relative;
  }
  .prose pre:not(.chroma) code {
    background-color: transparent;
    padding: calc(var(--spacing) * 0);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    color: var(--tw-prose-pre-code);
    font-weight: normal;
  }
  .prose table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid var(--color-border);
    box-shadow: 0 1px 3px 0 var(--color-foreground);
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px 3px 0 color-mix(in srgb, var(--color-foreground) 10%, transparent);
    }
  }
  .prose th {
    background-color: var(--color-muted);
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 3);
    text-align: left;
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    color: var(--tw-prose-headings);
    border-bottom: 1px solid var(--tw-prose-th-borders);
    background: var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      background: color-mix(in srgb, var(--color-primary) 5%, var(--color-muted));
    }
  }
  .prose td {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 3);
    border-bottom-color: var(--tw-prose-td-borders);
    color: var(--tw-prose-body);
  }
  .prose tr:last-child td {
    border-bottom: none;
  }
  .prose tr:hover td {
    background: var(--color-muted);
    @supports (color: color-mix(in lab, red, red)) {
      background: color-mix(in srgb, var(--color-muted) 50%, transparent);
    }
  }
  .prose hr {
    margin-block: calc(var(--spacing) * 8);
    height: 1px;
    border-style: var(--tw-border-style);
    border-width: 0px;
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    --tw-gradient-via: var(--color-border);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .prose img {
    margin-inline: auto;
    border-radius: var(--radius-lg);
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    margin: 0;
    max-width: 100%;
    height: auto;
  }
  .prose figure {
    margin: 2rem 0;
  }
  .prose figcaption {
    margin-top: calc(var(--spacing) * 2);
    text-align: center;
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    color: var(--tw-prose-captions);
    font-style: italic;
  }
  .prose strong {
    color: var(--tw-prose-bold);
    font-weight: 600;
  }
  .prose em {
    font-style: italic;
    color: var(--tw-prose-body);
  }
  .prose del, .prose s {
    text-decoration: line-through;
    color: var(--color-muted-foreground);
    opacity: 0.8;
  }
  .prose mark {
    background: linear-gradient( 135deg, var(--color-primary), var(--color-accent) );
    @supports (color: color-mix(in lab, red, red)) {
      background: linear-gradient( 135deg, color-mix(in srgb, var(--color-primary) 20%, transparent), color-mix(in srgb, var(--color-accent) 20%, transparent) );
    }
    color: var(--tw-prose-body);
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-weight: 500;
  }
  .prose sup {
    font-size: 0.75rem;
    line-height: 1;
    vertical-align: super;
    color: var(--color-primary);
    font-weight: 500;
  }
  .prose sub {
    font-size: 0.75rem;
    line-height: 1;
    vertical-align: sub;
    color: var(--color-primary);
    font-weight: 500;
  }
  .prose kbd {
    display: inline-flex;
    align-items: center;
    border-radius: 0.25rem;
    border-style: var(--tw-border-style);
    border-width: 1px;
    padding-inline: calc(var(--spacing) * 2);
    padding-block: calc(var(--spacing) * 1);
    font-family: var(--font-mono);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    background: var(--color-muted);
    color: var(--tw-prose-body);
    border-color: var(--color-border);
    box-shadow: 0 1px 2px var(--color-foreground);
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px 2px color-mix(in srgb, var(--color-foreground) 10%, transparent);
    }
    min-height: 1.5rem;
  }
  .prose abbr {
    text-decoration: underline;
    text-decoration-style: dotted;
    text-decoration-color: var(--color-primary);
    cursor: help;
  }
  .prose details {
    margin: 1.5rem 0;
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    overflow: hidden;
    background: var(--color-card);
  }
  .prose details summary {
    padding: 1rem 1.5rem;
    background: var(--color-muted);
    @supports (color: color-mix(in lab, red, red)) {
      background: color-mix(in srgb, var(--color-muted) 50%, transparent);
    }
    cursor: pointer;
    font-weight: 600;
    color: var(--tw-prose-headings);
    border-bottom: 1px solid var(--color-border);
    transition: all 0.2s ease;
    position: relative;
    user-select: none;
  }
  .prose details summary:hover {
    background: var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      background: color-mix( in srgb, var(--color-primary) 10%, var(--color-muted) );
    }
    color: var(--color-primary);
  }
  .prose details summary::marker {
    display: none;
  }
  .prose details summary::before {
    content: "▶";
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.2s ease;
    color: var(--color-primary);
    font-size: 0.875rem;
  }
  .prose details[open] summary::before {
    transform: translateY(-50%) rotate(90deg);
  }
  .prose details[open] summary {
    border-bottom-color: var(--color-border);
  }
  .prose details > *:not(summary) {
    padding: 1rem 1.5rem;
  }
  .prose details > *:last-child {
    padding-bottom: 1.5rem;
  }
  .code-block-container {
    background: var(--color-card);
  }
  .code-block-content {
    background: var(--color-muted);
    @supports (color: color-mix(in lab, red, red)) {
      background: color-mix(in srgb, var(--color-muted) 30%, transparent);
    }
  }
  .code-block-content .chroma {
    background-color: transparent !important;
  }
  .dark .code-block-content {
    background: var(--color-muted);
    @supports (color: color-mix(in lab, red, red)) {
      background: color-mix(in srgb, var(--color-muted) 50%, transparent);
    }
  }
  @media (max-width: 640px) {
    .prose {
      font-size: 0.9rem;
    }
    .prose h1 {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
    .prose h2 {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
    .prose h3 {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
}
@layer components {
  #toc-content nav {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }
  #toc-content ul {
    margin: calc(var(--spacing) * 0);
    list-style-type: none;
    padding: calc(var(--spacing) * 0);
  }
  #toc-content li {
    margin-block: calc(var(--spacing) * 1);
    padding: calc(var(--spacing) * 0);
  }
  #toc-content a {
    position: relative;
    display: block;
    border-radius: var(--radius-lg);
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
    border-color: transparent;
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 3);
    color: var(--color-muted-foreground);
    text-decoration-line: none;
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 200ms;
    transition-duration: 200ms;
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
    &:hover {
      @media (hover: hover) {
        --tw-translate-x: calc(var(--spacing) * 0.5);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
    &:hover {
      @media (hover: hover) {
        --tw-translate-y: -1px;
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
    &:hover {
      @media (hover: hover) {
        scale: 1.02;
      }
    }
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);
        }
      }
    }
    &:hover {
      @media (hover: hover) {
        color: var(--color-primary);
      }
    }
  }
  #toc-content a.active, #toc-content li.active > a {
    --tw-translate-x: calc(var(--spacing) * 0.5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
    --tw-translate-y: -1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
    scale: 1.02;
    background-color: var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);
    }
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--color-primary);
  }
  #toc-content ul ul a {
    padding-left: calc(var(--spacing) * 6);
  }
  #toc-content ul ul ul a {
    padding-left: calc(var(--spacing) * 8);
  }
  #toc-content ul ul ul ul a {
    padding-left: calc(var(--spacing) * 10);
  }
  #toc-content ul ul ul ul ul a {
    padding-left: calc(var(--spacing) * 12);
  }
  #toc-content ul ul ul ul ul ul a {
    padding-left: calc(var(--spacing) * 14);
  }
}
.toc-scrollbar::-webkit-scrollbar {
  width: 6px;
}
.toc-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}
.toc-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--color-border);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}
.toc-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-muted-foreground);
}
@layer components {
  .mobile-menu.dropdown-menu {
    max-width: calc(100vw - 2rem);
    min-width: 20rem;
  }
  @media (max-width: 480px) {
    .mobile-menu.dropdown-menu {
      min-width: 18rem;
      max-width: calc(100vw - 1rem);
    }
  }
  .dropdown-menu {
    min-width: 10rem;
    max-height: 20rem;
    overflow-y: auto;
  }
  .search-result-selected {
    background-color: var(--color-primary) !important;
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix( in srgb, var(--color-primary) 10%, transparent ) !important;
    }
    color: var(--color-primary) !important;
  }
  .scrollbar-hide {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  .reading-progress-container {
    height: var(--reading-progress-height);
  }
  .reading-progress-bg {
    height: var(--reading-progress-height);
    background-color: var(--reading-progress-bg);
  }
  .reading-progress-bar {
    height: var(--reading-progress-height);
    box-shadow: 0 0 8px var(--reading-progress-shadow);
  }
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  @media (max-width: 480px) {
    #dock span {
      display: none !important;
    }
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-tracking: initial;
    }
  }
}
