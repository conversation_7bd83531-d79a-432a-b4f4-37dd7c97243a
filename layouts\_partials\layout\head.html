{{/* 基础 Meta 标签 */}}
<meta charset="utf-8" />
<meta
  name="viewport"
  content="width=device-width, initial-scale=1, shrink-to-fit=no" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />

{{/* 页面标题 */}}
<title>
  {{- if .IsHome -}}
    {{ site.Title }}{{ with site.Params.site.description }}- {{ . }}{{ end }}
  {{- else if .Title -}}
    {{ .Title }} |
    {{ site.Title }}
  {{- else -}}
    {{ site.Title }}
  {{- end -}}
</title>

{{/* 基础 Meta 信息 */}}
<meta
  name="description"
  content="{{ with .Description }}
    {{ . }}
  {{ else }}
    {{ with .Summary }}
      {{ . }}
    {{ else }}
      {{ site.Params.site.description }}
    {{ end }}
  {{ end }}" />
<meta
  name="keywords"
  content="{{ with .Params.tags }}
    {{ delimit . ", " }}
  {{ else }}
    {{ delimit site.Params.site.keywords ", " }}
  {{ end }}" />
<meta
  name="author"
  content="{{ with .Params.author }}
    {{ . }}
  {{ else }}
    {{ site.Params.site.author }}
  {{ end }}" />
<meta
  name="language"
  content="{{ site.Params.site.language | default site.LanguageCode }}" />
<meta
  name="robots"
  content="{{ with .Params.robots }}
    {{ . }}
  {{ else }}
    index, follow
  {{ end }}
  " />

{{/* Canonical URL */}}
<link rel="canonical" href="{{ .Permalink }}" />

{{/* Favicon 配置 (现代 SVG 格式) */}}
{{ with site.Params.favicon.svg }}
  <link rel="icon" type="image/svg+xml" href="{{ . }}" />
{{ else }}
  {{/* 默认 SVG Favicon */}}
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
{{ end }}

{{/* Web App Manifest (自动生成) */}}
<link rel="manifest" href="/site.webmanifest" />

{{/* SEO Meta 标签 */}}
{{ partial "layout/head/seo.html" . }}

{{/* 样式表 */}}
{{ partial "layout/head/css.html" . }}

{{/* JavaScript */}}
{{ partial "layout/head/js.html" . }}
