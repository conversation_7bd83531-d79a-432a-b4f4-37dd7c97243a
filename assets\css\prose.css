/* Prose Typography 样式 - 从 main.css 提取 */

/* 自定义 Prose Typography */
@layer utilities {
  .prose {
    /* 基础文本样式 */
    --tw-prose-body: var(--color-foreground);
    --tw-prose-headings: var(--color-foreground);
    --tw-prose-lead: var(--color-muted-foreground);
    --tw-prose-links: var(--color-primary);
    --tw-prose-bold: var(--color-foreground);
    --tw-prose-counters: var(--color-muted-foreground);
    --tw-prose-bullets: var(--color-muted-foreground);
    --tw-prose-hr: var(--color-border);
    --tw-prose-quotes: var(--color-foreground);
    --tw-prose-quote-borders: var(--color-border);
    --tw-prose-captions: var(--color-muted-foreground);
    --tw-prose-code: var(--color-foreground);
    --tw-prose-pre-code: var(--color-muted-foreground);
    --tw-prose-pre-bg: var(--color-muted);
    --tw-prose-th-borders: var(--color-border);
    --tw-prose-td-borders: var(--color-border);

    /* 字体和间距 */
    font-family: ui-sans-serif, system-ui, sans-serif;
    line-height: 1.7;
    font-size: 1rem;
    max-width: none;
  }

  /* 标题样式 */
  .prose h1 {
    @apply scroll-m-20 text-4xl font-bold tracking-tight lg:text-5xl;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--tw-prose-headings);
    position: relative;
  }

  .prose h1::after {
    content: "";
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 3rem;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary), transparent);
    border-radius: 2px;
  }

  .prose h2 {
    @apply scroll-m-20 text-3xl font-semibold tracking-tight;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--tw-prose-headings);
    position: relative;
    padding-left: 1rem;
  }

  .prose h2::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0.25rem;
    width: 4px;
    height: 1.5rem;
    background: var(--color-primary);
    border-radius: 2px;
  }

  .prose h3 {
    @apply scroll-m-20 text-2xl font-semibold tracking-tight;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--tw-prose-headings);
  }

  .prose h4 {
    @apply scroll-m-20 text-xl font-semibold tracking-tight;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--tw-prose-headings);
  }
  .prose h4[id^="alert-"] {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .prose h5 {
    @apply scroll-m-20 text-lg font-semibold tracking-tight;
    margin-top: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--tw-prose-headings);
  }

  .prose h6 {
    @apply scroll-m-20 text-base font-semibold tracking-tight;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    color: var(--tw-prose-headings);
  }

  /* 段落和文本 */
  .prose p {
    @apply leading-7;
    margin-top: 1.25rem;
    margin-bottom: 1.25rem;
    color: var(--tw-prose-body);
  }

  .prose p:first-child {
    margin-top: 0;
  }

  .prose p:last-child {
    margin-bottom: 0;
  }

  /* 引用块 */
  .prose blockquote {
    @apply border-l-4 pl-6 italic;
    border-left-color: var(--color-primary);
    background: color-mix(in srgb, var(--color-muted) 30%, transparent);
    margin: 1.5rem 0;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem; /* 添加圆角，使其更圆润 */
    border-top-left-radius: 0; /* 保持左上角直角以配合左边框 */
    border-bottom-left-radius: 0; /* 保持左下角直角以配合左边框 */
    position: relative;
  }

  .prose blockquote::before {
    content: '"';
    font-size: 4rem;
    color: color-mix(in srgb, var(--color-primary) 20%, transparent);
    position: absolute;
    top: -0.5rem;
    left: 1rem;
    font-family: serif;
    line-height: 1;
  }

  .prose blockquote p {
    margin: 0;
    font-style: italic;
    font-size: 1.1rem;
    color: var(--tw-prose-quotes);
  }

  /* 嵌套引用 */
  .prose blockquote blockquote {
    margin: 1rem 0;
    border-left-color: var(--color-accent);
    background: color-mix(in srgb, var(--color-accent) 10%, transparent);
  }

  .prose blockquote blockquote::before {
    color: color-mix(in srgb, var(--color-accent) 20%, transparent);
  }

  /* 脚注 */
  .prose .footnote-ref {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    vertical-align: super;
    line-height: 1;
  }

  .prose .footnote-ref:hover {
    color: var(--color-accent);
    text-decoration: underline;
  }

  .prose .footnotes {
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--color-border);
    font-size: 0.875rem;
  }

  .prose .footnotes ol {
    padding-left: 1rem;
  }

  .prose .footnotes li {
    margin: 0.5rem 0;
  }

  .prose .footnote-backref {
    color: var(--color-primary);
    text-decoration: none;
    margin-left: 0.5rem;
  }

  .prose .footnote-backref:hover {
    color: var(--color-accent);
    text-decoration: underline;
  }

  /* 链接样式 */
  .prose a {
    color: var(--tw-prose-links);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
    background: linear-gradient(
      to bottom,
      transparent 50%,
      color-mix(in srgb, var(--color-primary) 15%, transparent) 50%
    );
    background-size: 100% 200%;
    background-position: 0 0;
  }

  .prose a::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--color-primary);
    transition: width 0.3s ease;
  }

  .prose a:hover {
    color: var(--color-primary);
    background-position: 0 100%;
  }

  .prose a:hover::after {
    width: 100%;
  }

  /* 列表样式 */
  .prose ul {
    list-style: none;
    padding-left: 0;
    margin: 1.25rem 0;
  }

  .prose ul li {
    position: relative;
    padding-left: 1rem;
    margin: 0.5rem 0.6rem;
    color: var(--tw-prose-body);
  }

  .prose ul li::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0.75rem;
    width: 6px;
    height: 6px;
    background: var(--color-primary);
    border-radius: 50%;
  }

  .prose ol {
    counter-reset: item;
    padding-left: 0;
    margin: 1.25rem 0;
    list-style: none; /* 隐藏默认的数字序号 */
  }

  .prose ol li {
    position: relative;
    padding-left: 2rem;
    margin: 0.5rem 0.6rem;
    counter-increment: item;
    color: var(--tw-prose-body);
  }

  .prose ol li::before {
    content: counter(item);
    position: absolute;
    left: 0;
    top: 0.2rem;
    background: var(--color-primary);
    color: white;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    line-height: 1; /* 确保数字垂直居中 */
  }

  /* 任务列表 (Checkbox) */
  .prose ul li.task-list-item {
    list-style: none;
    position: relative;
    padding-left: 2rem;
    margin: 0.5rem 0;
  }

  .prose ul li.task-list-item::before {
    display: none; /* 隐藏默认的圆点 */
  }

  .prose ul li.task-list-item input[type="checkbox"] {
    position: absolute;
    left: 0;
    top: 0.5rem;
    width: 1rem;
    height: 1rem;
    margin: 0;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    border: 2px solid var(--color-border);
    border-radius: 0.25rem;
    background: var(--color-background);
    transition: all 0.2s ease;
  }

  .prose ul li.task-list-item input[type="checkbox"]:checked {
    background: var(--color-primary);
    border-color: var(--color-primary);
  }

  .prose ul li.task-list-item input[type="checkbox"]:checked::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
  }

  .prose ul li.task-list-item input[type="checkbox"]:hover {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px
      color-mix(in srgb, var(--color-primary) 20%, transparent);
  }

  /* 定义列表 */
  .prose dl {
    margin: 1.5rem 0;
  }

  .prose dt {
    font-weight: 600;
    color: var(--tw-prose-headings);
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1rem;
  }

  .prose dt::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0.5rem;
    width: 4px;
    height: 4px;
    background: var(--color-primary);
    border-radius: 50%;
  }

  .prose dd {
    margin-left: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--tw-prose-body);
    padding-left: 1rem;
    border-left: 2px solid
      color-mix(in srgb, var(--color-border) 50%, transparent);
  }

  /* 嵌套列表样式改进 */
  .prose ul ul,
  .prose ol ol,
  .prose ul ol,
  .prose ol ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
  }

  /* 二级无序列表使用方形标记 */
  .prose ul ul li::before {
    width: 4px;
    height: 4px;
    border-radius: 0;
    background: var(--color-accent);
  }

  /* 三级无序列表使用三角形标记 */
  .prose ul ul ul li::before {
    content: "";
    width: 0;
    height: 0;
    border-left: 3px solid var(--color-accent);
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    border-radius: 0;
    background: transparent;
    top: 0.8rem;
  }

  /* 嵌套有序列表样式调整 */
  .prose ol ol li::before {
    background: var(--color-accent);
    font-size: 0.7rem;
    width: 1.1rem;
    height: 1.1rem;
  }

  .prose ol ol ol li::before {
    background: var(--color-muted-foreground);
    font-size: 0.65rem;
    width: 1rem;
    height: 1rem;
  }

  /* 代码样式 */
  .prose code {
    @apply bg-muted rounded px-1.5 py-0.5 font-mono text-sm;
    color: var(--tw-prose-code);
    font-weight: 600;
  }

  /* 代码块样式 - 与 render hook 和 Chroma 配合 */
  .code-block-container {
    margin: 1.5rem 0;
  }

  /* Chroma 生成的代码块样式 */
  .code-block-content .chroma {
    @apply overflow-x-auto p-4 font-mono text-sm;
    margin: 0;
    border: none;
    border-radius: 0;
    background: transparent !important; /* 让主题背景生效 */
  }

  .code-block-content .chroma pre {
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    overflow: visible;
  }

  .code-block-content .chroma code {
    @apply bg-transparent p-0;
    font-weight: normal;
    font-family: inherit;
    background: transparent;
  }

  /* 内联行号样式 */
  .code-block-content .chroma .ln {
    user-select: none; /* 行号不可选择 */
    margin-right: 0.4em;
    padding: 0 0.4em 0 0.4em;
    color: var(--chroma-ln, #7f7f7f);
  }

  .code-block-content .chroma .line {
    display: flex;
  }

  .code-block-content .chroma .cl {
    flex: 1;
  }

  /* 代码块折叠功能样式 */
  .collapse-overlay {
    cursor: pointer;
    transition: opacity 0.3s ease;
  }

  .collapse-overlay:hover {
    backdrop-filter: blur(1px);
  }

  .collapse-overlay > div {
    transition: transform 0.2s ease;
  }

  .collapse-overlay:hover > div {
    transform: translateX(-50%) scale(1.05);
  }

  /* 折叠状态下的代码块内容 */
  .code-block-content {
    transition: max-height 0.3s ease-out;
  }

  /* 内联代码样式保持不变 */
  .prose code:not(.chroma code) {
    @apply bg-muted rounded px-1.5 py-0.5 font-mono text-sm;
    color: var(--tw-prose-code);
    font-weight: 600;
  }

  /* 传统 pre 样式（作为后备） */
  .prose pre:not(.chroma) {
    @apply overflow-x-auto p-4;
    background: var(--tw-prose-pre-bg);
    margin: 0;
    position: relative;
  }

  .prose pre:not(.chroma) code {
    @apply bg-transparent p-0 text-sm;
    color: var(--tw-prose-pre-code);
    font-weight: normal;
  }

  /* 表格样式 */
  .prose table {
    @apply w-full border-collapse;
    margin: 1.5rem 0;
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid var(--color-border);
    box-shadow: 0 1px 3px 0
      color-mix(in srgb, var(--color-foreground) 10%, transparent);
  }

  .prose th {
    @apply bg-muted px-4 py-3 text-left font-semibold;
    color: var(--tw-prose-headings);
    border-bottom: 1px solid var(--tw-prose-th-borders);
    background: color-mix(in srgb, var(--color-primary) 5%, var(--color-muted));
  }

  .prose td {
    @apply border-b px-4 py-3;
    border-bottom-color: var(--tw-prose-td-borders);
    color: var(--tw-prose-body);
  }

  .prose tr:last-child td {
    border-bottom: none;
  }

  .prose tr:hover td {
    background: color-mix(in srgb, var(--color-muted) 50%, transparent);
  }

  /* 分隔线 */
  .prose hr {
    @apply via-border my-8 h-px border-0 bg-gradient-to-r from-transparent to-transparent;
  }

  /* 图片样式 */
  .prose img {
    @apply mx-auto rounded-lg shadow-md;
    margin: 0;
    max-width: 100%;
    height: auto;
  }

  .prose figure {
    margin: 2rem 0;
  }

  .prose figcaption {
    @apply mt-2 text-center text-sm;
    color: var(--tw-prose-captions);
    font-style: italic;
  }

  /* 强调文本 */
  .prose strong {
    color: var(--tw-prose-bold);
    font-weight: 600;
  }

  .prose em {
    font-style: italic;
    color: var(--tw-prose-body);
  }

  /* 删除线文本 */
  .prose del,
  .prose s {
    text-decoration: line-through;
    color: var(--color-muted-foreground);
    opacity: 0.8;
  }

  /* 高亮文本 */
  .prose mark {
    background: linear-gradient(
      135deg,
      color-mix(in srgb, var(--color-primary) 20%, transparent),
      color-mix(in srgb, var(--color-accent) 20%, transparent)
    );
    color: var(--tw-prose-body);
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-weight: 500;
  }

  /* 上标和下标 */
  .prose sup {
    font-size: 0.75rem;
    line-height: 1;
    vertical-align: super;
    color: var(--color-primary);
    font-weight: 500;
  }

  .prose sub {
    font-size: 0.75rem;
    line-height: 1;
    vertical-align: sub;
    color: var(--color-primary);
    font-weight: 500;
  }

  /* 键盘按键 */
  .prose kbd {
    @apply inline-flex items-center rounded border px-2 py-1 font-mono text-xs font-semibold;
    background: var(--color-muted);
    color: var(--tw-prose-body);
    border-color: var(--color-border);
    box-shadow: 0 1px 2px
      color-mix(in srgb, var(--color-foreground) 10%, transparent);
    min-height: 1.5rem;
  }

  /* 缩写 */
  .prose abbr {
    text-decoration: underline;
    text-decoration-style: dotted;
    text-decoration-color: var(--color-primary);
    cursor: help;
  }

  /* 详情折叠 (Details/Summary) */
  .prose details {
    margin: 1.5rem 0;
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    overflow: hidden;
    background: var(--color-card);
  }

  .prose details summary {
    padding: 1rem 1.5rem;
    background: color-mix(in srgb, var(--color-muted) 50%, transparent);
    cursor: pointer;
    font-weight: 600;
    color: var(--tw-prose-headings);
    border-bottom: 1px solid var(--color-border);
    transition: all 0.2s ease;
    position: relative;
    user-select: none;
  }

  .prose details summary:hover {
    background: color-mix(
      in srgb,
      var(--color-primary) 10%,
      var(--color-muted)
    );
    color: var(--color-primary);
  }

  .prose details summary::marker {
    display: none;
  }

  .prose details summary::before {
    content: "▶";
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.2s ease;
    color: var(--color-primary);
    font-size: 0.875rem;
  }

  .prose details[open] summary::before {
    transform: translateY(-50%) rotate(90deg);
  }

  .prose details[open] summary {
    border-bottom-color: var(--color-border);
  }

  .prose details > *:not(summary) {
    padding: 1rem 1.5rem;
  }

  .prose details > *:last-child {
    padding-bottom: 1.5rem;
  }

  /* 语法高亮主题适配 */
  .code-block-container {
    /* 代码块容器背景适配主题 */
    background: var(--color-card);
  }

  .code-block-content {
    /* 代码块内容背景适配主题 */
    background: color-mix(in srgb, var(--color-muted) 30%, transparent);
  }

  /* 确保 chroma 背景与主题一致 */
  .code-block-content .chroma {
    background-color: transparent !important;
  }

  /* 暗色模式下的代码块背景 */
  .dark .code-block-content {
    background: color-mix(in srgb, var(--color-muted) 50%, transparent);
  }

  /* Alert 样式 - 通过 render hook 实现，这里只保留基础样式 */

  /* 响应式调整 */
  @media (max-width: 640px) {
    .prose {
      font-size: 0.9rem;
    }

    .prose h1 {
      @apply text-3xl;
    }

    .prose h2 {
      @apply text-2xl;
    }

    .prose h3 {
      @apply text-xl;
    }
  }
}
