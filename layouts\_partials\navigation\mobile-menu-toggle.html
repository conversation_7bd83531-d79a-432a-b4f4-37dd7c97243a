<!-- 移动端菜单按钮 -->
<div class="relative">
  <button
    id="mobile-menu-toggle"
    class="dropdown-toggle border-border bg-background text-muted-foreground hover:bg-accent hover:text-foreground flex h-10 w-10 items-center justify-center rounded-lg border transition-colors duration-200"
    data-dropdown-type="mobile-menu"
    aria-label="{{ i18n "nav.menu" }}"
    aria-expanded="false"
    aria-haspopup="true">
    {{ partial "features/icon.html" (dict "name" "menu" "size" "md" "ariaLabel" (i18n "nav.menu")) }}
  </button>

  <!-- 移动端导航面板 -->
  <div
    id="mobile-menu"
    class="mobile-menu dropdown-menu border-border bg-popover/95 absolute top-12 left-0 z-[60] hidden w-80 max-w-[calc(100vw-2rem)] rounded-lg border p-1 shadow-lg backdrop-blur-sm transition-all duration-200 ease-out md:hidden"
    data-dropdown-type="mobile-menu"
    role="menu"
    aria-labelledby="mobile-menu-toggle">
    <nav class="flex flex-col">
      {{ $currentPage := . }}
      {{ range site.Menus.main }}
        {{ $isActive := eq $currentPage.RelPermalink .URL }}
        <a
          href="{{ .URL }}"
          class="nav-link {{ if $isActive }}
            nav-active-indicator bg-accent text-accent-foreground
          {{ else }}
            text-muted-foreground hover:text-primary hover:bg-primary/10
          {{ end }} focus:ring-primary/20 relative flex items-center gap-3 rounded-md px-4 py-3 text-sm font-medium transition-all duration-200 ease-out hover:translate-x-1 focus:ring-2 focus:outline-none"
          role="menuitem"
          {{ if $isActive }}aria-current="page"{{ end }}>
          {{ if .Params.icon }}
            {{ partial "features/icon.html" (dict "name" .Params.icon "size" "md" "ariaLabel" .Name) }}
          {{ end }}
          <span
            >{{ if hasPrefix .Name "nav." }}
              {{ i18n .Name }}
            {{ else }}
              {{ .Name }}
            {{ end }}</span
          >
        </a>
      {{ end }}
    </nav>
  </div>
</div>
