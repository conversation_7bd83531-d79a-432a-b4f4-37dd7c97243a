<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="100" cy="100" r="100" fill="url(#gradient)"/>
  
  <!-- 人物头像 -->
  <circle cx="100" cy="80" r="30" fill="white" opacity="0.9"/>
  <path d="M60 160 Q100 140 140 160 L140 200 L60 200 Z" fill="white" opacity="0.9"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
