{{/* 
  封面图片处理器 - 统一的图片获取和处理逻辑
  
  输入：页面对象 (.)
  输出：设置以下变量到上下文
  - $coverImage: 处理后的图片URL
  - $coverProcessed: 是否成功处理
  - $gradients: 渐变色数组
  - $colorIndex: 颜色索引
*/}}

{{/* 处理封面图片 */}}
{{ $coverImage := "" }}
{{ $coverProcessed := false }}

{{ if .Params.cover }}
  {{ $coverSrc := .Params.cover }}
  
  {{/* 检查是否为外部URL */}}
  {{ if hasPrefix $coverSrc "http" }}
    {{/* 外部图片：尝试获取远程资源 */}}
    {{ with try (resources.GetRemote $coverSrc) }}
      {{ with .Err }}
        {{/* 远程资源获取失败，使用原始URL */}}
        {{ $coverImage = $coverSrc }}
      {{ else with .Value }}
        {{/* 远程资源获取成功，进行处理 */}}
        {{ $coverImage = .RelPermalink }}
        {{ $coverProcessed = true }}
      {{ end }}
    {{ else }}
      {{/* 获取远程资源失败，使用原始URL */}}
      {{ $coverImage = $coverSrc }}
    {{ end }}
  {{ else }}
    {{/* 本地图片：检查页面资源或全局资源 */}}
    {{ $localPath := $coverSrc }}
    
    {{/* 处理各种可能的路径格式 */}}
    {{ if hasPrefix $coverSrc "./" }}
      {{ $localPath = strings.TrimPrefix "./" $coverSrc }}
    {{ else if hasPrefix $coverSrc "/" }}
      {{ $localPath = strings.TrimPrefix "/" $coverSrc }}
    {{ end }}
    
    {{/* 首先尝试页面资源 */}}
    {{ with .Resources.Get $localPath }}
      {{/* 页面资源找到 */}}
      {{ $coverImage = .RelPermalink }}
      {{ $coverProcessed = true }}
    {{ else with .Resources.Get $coverSrc }}
      {{/* 尝试原始路径作为页面资源 */}}
      {{ $coverImage = .RelPermalink }}
      {{ $coverProcessed = true }}
    {{ else with resources.Get $coverSrc }}
      {{/* 全局资源 */}}
      {{ $coverImage = .RelPermalink }}
      {{ $coverProcessed = true }}
    {{ else with resources.Get $localPath }}
      {{/* 尝试处理后的路径作为全局资源 */}}
      {{ $coverImage = .RelPermalink }}
      {{ $coverProcessed = true }}
    {{ else }}
      {{/* 资源未找到，使用原始路径 */}}
      {{ $coverImage = $coverSrc }}
    {{ end }}
  {{ end }}
{{ end }}

{{/* 统一的占位图生成逻辑 */}}
{{ $hash := md5 .Title }}
{{ $hexColor := substr $hash 0 2 }}
{{ $colorIndex := mod (int (printf "0x%s" $hexColor)) 6 }}

{{ $gradients := slice
  "from-blue-500/20 to-purple-500/10"
  "from-green-500/20 to-teal-500/10"
  "from-orange-500/20 to-red-500/10"
  "from-purple-500/20 to-pink-500/10"
  "from-teal-500/20 to-cyan-500/10"
  "from-rose-500/20 to-orange-500/10"
}}

{{/* 将变量设置到上下文中供调用者使用 */}}
{{ $.Scratch.Set "coverImage" $coverImage }}
{{ $.Scratch.Set "coverProcessed" $coverProcessed }}
{{ $.Scratch.Set "gradients" $gradients }}
{{ $.Scratch.Set "colorIndex" $colorIndex }}
